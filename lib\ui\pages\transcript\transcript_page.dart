import 'dart:async';

import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:just_audio/just_audio.dart';
import 'package:note_x/lib.dart';

class TranscriptPage extends StatefulWidget {
  final List<TranscriptModel> transcriptJson;
  final String transcript;
  final NoteModel note;
  final QuillController quillTranscriptController;
  final bool isCommunityNote;
  final AudioPlayer? audioPlayer;
  final AudioPlayerInterface? audioPlayerInterface;
  final MyNoteDetailCubit cubit;

  const TranscriptPage({
    Key? key,
    required this.transcriptJson,
    required this.transcript,
    required this.note,
    required this.quillTranscriptController,
    required this.cubit,
    this.isCommunityNote = false,
    this.audioPlayer,
    this.audioPlayerInterface,
  }) : super(key: key);

  @override
  State<TranscriptPage> createState() => _TranscriptPageState();
}

// We'll keep this for future reference in case we need to access the state directly
// typedef TranscriptPageState = _TranscriptPageState;

class _TranscriptPageState extends State<TranscriptPage> {
  final ScrollController _scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();
  final ValueNotifier<Duration> _currentPosition = ValueNotifier(Duration.zero);
  final ValueNotifier<int> _highlightedIndexNotifier = ValueNotifier(-1);
  StreamSubscription<Duration>? _positionSubscription;
  final Map<int, GlobalKey<State<TranscriptLineWidget>>> _itemKeys = {};
  bool _isScrolling = false;
  bool _isEditing = false;
  Duration? _lastAudioPosition;

  // Added for improved transcript editing
  final bool _useSpecializedEditor =
      true; // Set to true to use the TranscriptItemsEditor
  List<TranscriptEditItem> _transcriptEditItems = [];

  bool _isUpdatingQuillController = false;

  @override
  void initState() {
    super.initState();
    _setupAudioPlayerListener();
    _rebuildItemKeys();

    widget.quillTranscriptController.document.changes.listen((event) {
      if (event.source == ChangeSource.local && !_isUpdatingQuillController) {
        widget.cubit.setHasEdits(true);

        if (_isEditing && _useSpecializedEditor) {
          final plainText =
              widget.quillTranscriptController.document.toPlainText();
          final items = TranscriptEditItem.parseFromText(plainText);

          if (items.length != _transcriptEditItems.length ||
              !_areTranscriptItemsEqual(items, _transcriptEditItems)) {
            setState(() {
              _transcriptEditItems = items;
            });
          }
        }
      }
    });

    widget.cubit.stream.listen((state) {
      if (_isEditing && !state.hasEdits) {
        saveTranscriptChanges();

        setState(() {
          _isEditing = false;
        });

        WidgetsBinding.instance.addPostFrameCallback((_) {
          _rebuildItemKeys();

          // Restore position from audio player widget if available
          if (widget.audioPlayerInterface != null) {
            widget.audioPlayerInterface!.restorePosition().then((_) {
              // After audio player restoration, also do our own restoration
              // Add a small delay to ensure everything is properly synchronized
              Future.delayed(const Duration(milliseconds: 100), () {
                _reconnectAudioPlayerAndHighlighting();
              });
            }).catchError((error) {
              debugPrint('TranscriptPage: Error in audio player interface restoration: $error');
              // Still try to reconnect even if restoration failed
              _reconnectAudioPlayerAndHighlighting();
            });
          } else {
            _reconnectAudioPlayerAndHighlighting();
          }
        });
      }
    });
  }

  bool _areTranscriptItemsEqual(
      List<TranscriptEditItem> a, List<TranscriptEditItem> b) {
    if (a.length != b.length) return false;

    for (int i = 0; i < a.length; i++) {
      if (a[i].timestamp != b[i].timestamp || a[i].content != b[i].content) {
        return false;
      }
    }

    return true;
  }

  void _rebuildItemKeys() {
    _itemKeys.clear();
    for (int i = 0; i < widget.transcriptJson.length; i++) {
      _itemKeys[i] = GlobalKey<State<TranscriptLineWidget>>();
    }
  }

  void _reconnectAudioPlayerAndHighlighting() {
    // Cancel existing subscription
    _positionSubscription?.cancel();

    // Reinitialize audio player listener
    _setupAudioPlayerListener();

    // Restore audio position if we saved it
    if (_lastAudioPosition != null && widget.audioPlayer != null) {
      debugPrint('TranscriptPage: Restoring audio position ${_lastAudioPosition?.inSeconds}s');

      // Use the audio player interface if available
      if (widget.audioPlayerInterface != null) {
        widget.audioPlayerInterface!.seekToPosition(_lastAudioPosition!).then((_) {
          // Update the current position notifier
          _currentPosition.value = _lastAudioPosition!;

          // Force refresh highlight state
          _forceRefreshHighlight(_lastAudioPosition!);

          // Clear the saved position after restoration
          _lastAudioPosition = null;

          debugPrint('TranscriptPage: Audio position restoration completed');
        }).catchError((error) {
          debugPrint('TranscriptPage: Error restoring audio position: $error');
          _lastAudioPosition = null;
        });
      } else {
        // Fallback to direct audio player seek
        widget.audioPlayer!.seek(_lastAudioPosition!).then((_) {
          // Update the current position notifier
          _currentPosition.value = _lastAudioPosition!;

          // Force refresh highlight state
          _forceRefreshHighlight(_lastAudioPosition!);

          // Clear the saved position after restoration
          _lastAudioPosition = null;

          debugPrint('TranscriptPage: Audio position restoration completed (fallback)');
        }).catchError((error) {
          debugPrint('TranscriptPage: Error restoring audio position (fallback): $error');
          _lastAudioPosition = null;
        });
      }
    } else {
      debugPrint('TranscriptPage: No saved audio position to restore');
    }
  }

  void _setupAudioPlayerListener() {
    if (widget.audioPlayer != null) {
      debugPrint('TranscriptPage: Setting up audio player listener');
      _positionSubscription =
          widget.audioPlayer!.positionStream.listen((position) {
        _currentPosition.value = position;
        _updateHighlightedTranscript(position);
      });
    } else {
      debugPrint('TranscriptPage: No audio player available for listener setup');
    }
  }

  void _forceRefreshHighlight(Duration position) {
    debugPrint('TranscriptPage: Force refreshing highlight for position ${position.inSeconds}s');

    // Reset highlighted index to force refresh
    _highlightedIndexNotifier.value = -1;

    // Wait a frame then update highlight
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateHighlightedTranscript(position);
    });
  }

  void _updateHighlightedTranscript(Duration position) {
    if (widget.transcriptJson.isEmpty) return;

    final seconds = position.inMilliseconds / 1000;
    int newHighlightedIndex = -1;
    final int previousHighlightedIndex = _highlightedIndexNotifier.value;

    if (seconds < widget.transcriptJson.first.start) {
      if (_highlightedIndexNotifier.value != -1) {
        _highlightedIndexNotifier.value = -1;

        // Scroll to the beginning when audio position is reset to before first transcript
        if (_scrollController.hasClients && !_isScrolling) {
          _isScrolling = true;
          _scrollController
              .animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          )
              .then((_) {
            _isScrolling = false;
          });
        }
      }
      return;
    }

    int left = 0;
    int right = widget.transcriptJson.length - 1;

    while (left <= right) {
      int mid = (left + right) ~/ 2;
      final transcript = widget.transcriptJson[mid];
      final nextTranscript = mid < widget.transcriptJson.length - 1
          ? widget.transcriptJson[mid + 1]
          : null;

      if (nextTranscript != null) {
        if (seconds >= transcript.start && seconds < nextTranscript.start) {
          newHighlightedIndex = mid;
          break;
        } else if (seconds < transcript.start) {
          right = mid - 1;
        } else {
          left = mid + 1;
        }
      } else {
        if (seconds >= transcript.start) {
          newHighlightedIndex = mid;
        }
        break;
      }
    }

    if (newHighlightedIndex == -1 && left < widget.transcriptJson.length) {
      newHighlightedIndex = left;
    }

    if (newHighlightedIndex != previousHighlightedIndex) {
      _highlightedIndexNotifier.value = newHighlightedIndex;

      final bool isSeekingBackward =
          newHighlightedIndex < previousHighlightedIndex;

      if (!_isScrolling && newHighlightedIndex >= 0) {
        if (_scrollDebounce?.isActive ?? false) {
          _scrollDebounce!.cancel();
        }

        _scrollDebounce = Timer(const Duration(milliseconds: 50), () {
          _scrollToHighlightedItem(newHighlightedIndex, isSeekingBackward);
        });
      }
    }
  }

  Timer? _scrollDebounce;

  void _scrollToHighlightedItem(int index, [bool isSeekingBackward = false]) {
    if (index < 0 || !_scrollController.hasClients) return;

    _isScrolling = true;

    final key = _itemKeys[index];
    if (key?.currentContext == null) {
      _isScrolling = false;
      return;
    }

    final RenderBox? renderBox =
        key?.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null || !renderBox.hasSize) {
      _isScrolling = false;
      return;
    }

    final ScrollableState scrollableState = Scrollable.of(key!.currentContext!);

    final RenderBox scrollableBox =
        scrollableState.context.findRenderObject() as RenderBox;
    final viewportHeight = _scrollController.position.viewportDimension;
    final scrollableOffset = scrollableBox.localToGlobal(Offset.zero);

    final RenderAbstractViewport viewport =
        RenderAbstractViewport.of(renderBox);
    final RevealedOffset offset = viewport.getOffsetToReveal(renderBox, 0.0);

    double targetOffset;
    if (isSeekingBackward) {
      targetOffset = offset.offset - (viewportHeight * 0.15);
    } else {
      targetOffset = offset.offset - (viewportHeight * 0.3);
    }

    final double boundedOffset = targetOffset.clamp(
      0.0,
      _scrollController.position.maxScrollExtent,
    );

    final itemOffset = renderBox.localToGlobal(Offset.zero);
    final itemHeight = renderBox.size.height;
    final bool isItemVisible = itemOffset.dy >= scrollableOffset.dy &&
        itemOffset.dy + itemHeight <= scrollableOffset.dy + viewportHeight;

    if (!isItemVisible || isSeekingBackward) {
      final duration = isSeekingBackward
          ? const Duration(milliseconds: 200)
          : const Duration(milliseconds: 300);

      final curve = isSeekingBackward ? Curves.easeOut : Curves.easeInOut;

      if ((boundedOffset - _scrollController.position.pixels).abs() > 1.0) {
        _scrollController
            .animateTo(
          boundedOffset,
          duration: duration,
          curve: curve,
        )
            .then((_) {
          _isScrolling = false;
        });
      } else {
        _isScrolling = false;
      }
    } else {
      _isScrolling = false;
    }
  }

  // Track the index of the transcript being edited
  int _editingTranscriptIndex = -1;

  void startEditing(TranscriptModel transcript) {
    if (widget.isCommunityNote) return;

    AnalyticsService.logAnalyticsEventNoParam(
      eventName: EventName.transcript_edit_start,
    );

    // Save current audio position before editing and preserve it in the audio player
    // Only save if we're not already editing (to prevent overwriting saved position)
    if (widget.audioPlayer != null && !_isEditing) {
      _lastAudioPosition = widget.audioPlayer!.position;
      debugPrint('TranscriptPage: Saved audio position: ${_lastAudioPosition?.inSeconds}s');

      // Also preserve position in the audio player widget
      if (widget.audioPlayerInterface != null) {
        widget.audioPlayerInterface!.preservePosition();
      }
    } else if (_isEditing) {
      debugPrint('TranscriptPage: Already editing, keeping existing saved position: ${_lastAudioPosition?.inSeconds}s');
    }

    // Cancel audio player subscription during editing
    _positionSubscription?.cancel();
    _positionSubscription = null;

    // Find the index of the transcript being edited
    _editingTranscriptIndex = widget.transcriptJson.indexOf(transcript);

    _transcriptEditItems = widget.transcriptJson
        .map((t) => TranscriptEditItem(
      timestamp: formatTranscriptTime(t.start),
      content: t.text,
    ))
        .toList();

    final fullText = TranscriptEditItem.formatToText(_transcriptEditItems);
    final document = Document();
    document.insert(0, fullText);

    _isUpdatingQuillController = true;
    try {
      widget.quillTranscriptController.document = document;
    } finally {
      _isUpdatingQuillController = false;
    }

    setState(() {
      _isEditing = true;
    });

    if (!_useSpecializedEditor) {
      Future.delayed(const Duration(milliseconds: 100), () {
        _focusNode.requestFocus();

        // Set cursor position to the end of the text for the selected transcript
        if (_editingTranscriptIndex >= 0) {
          final text = widget.quillTranscriptController.document.toPlainText();
          final lines = text.split('\n\n');

          if (_editingTranscriptIndex < lines.length) {
            // Calculate the position of the end of the selected transcript
            int position = 0;
            for (int i = 0; i <= _editingTranscriptIndex; i++) {
              position += lines[i].length;
              if (i < _editingTranscriptIndex) {
                position += 2; // Add 2 for the '\n\n' between lines
              }
            }

            // Set the cursor position
            widget.quillTranscriptController.updateSelection(
              TextSelection.fromPosition(TextPosition(offset: position)),
              ChangeSource.local,
            );
          }
        }
      });
    }

    widget.cubit.setHasEdits(true);
  }

  bool _isSaving = false;

  void saveTranscriptChanges() {
    if (_isSaving || !_isEditing) return;

    _isSaving = true;

    try {
      // Track save event
      AnalyticsService.logAnalyticsEventNoParam(
        eventName: EventName.transcript_edit_save,
      );

      List<TranscriptEditItem> editItems;

      if (_useSpecializedEditor) {
        editItems = _transcriptEditItems;
      } else {
        final editedText =
        widget.quillTranscriptController.document.toPlainText();
        editItems = TranscriptEditItem.parseFromText(editedText);
      }

      final List<TranscriptModel> updatedTranscripts = [];

      for (int i = 0;
      i < widget.transcriptJson.length && i < editItems.length;
      i++) {
        final originalTranscript = widget.transcriptJson[i];
        final editItem = editItems[i];

        updatedTranscripts.add(TranscriptModel(
          start: originalTranscript.start,
          text: editItem.content,
          duration: originalTranscript.duration,
          speaker: originalTranscript.speaker,
        ));
      }

      if (editItems.length > widget.transcriptJson.length) {
        for (int i = widget.transcriptJson.length; i < editItems.length; i++) {
          final editItem = editItems[i];

          final parts = editItem.timestamp.split(':');
          double startTime = 0;
          double duration = 1.0;
          String speaker = '';

          if (parts.length == 3) {
            try {
              final hours = int.parse(parts[0]);
              final minutes = int.parse(parts[1]);
              final seconds = int.parse(parts[2]);
              startTime = hours * 3600 + minutes * 60 + seconds.toDouble();

              if (i < editItems.length - 1) {
                final nextParts = editItems[i + 1].timestamp.split(':');
                if (nextParts.length == 3) {
                  final nextHours = int.parse(nextParts[0]);
                  final nextMinutes = int.parse(nextParts[1]);
                  final nextSeconds = int.parse(nextParts[2]);
                  final nextStartTime = nextHours * 3600 +
                      nextMinutes * 60 +
                      nextSeconds.toDouble();
                  duration = nextStartTime - startTime;
                }
              }

              if (i > 0 && updatedTranscripts.isNotEmpty) {
                speaker = updatedTranscripts[i - 1].speaker;
              }
            } catch (e) {
              if (updatedTranscripts.isNotEmpty) {
                startTime = updatedTranscripts.last.start + 5;
                speaker = updatedTranscripts.last.speaker;
              }
            }
          }

          updatedTranscripts.add(TranscriptModel(
            start: startTime,
            text: editItem.content,
            duration: duration,
            speaker: speaker,
          ));
        }
      }

      final fullText = widget.quillTranscriptController.document.toPlainText();

      if (widget.cubit.state.hasEdits) {
        widget.cubit.onEditNote(
          note: widget.note,
          transcript: fullText,
          transcriptJson: updatedTranscripts,
        );
      } else {
        if (_useSpecializedEditor) {
          final formattedText = TranscriptEditItem.formatToText(editItems);
          final document = Document();
          document.insert(0, formattedText);

          _isUpdatingQuillController = true;
          try {
            widget.quillTranscriptController.document = document;
          } finally {
            _isUpdatingQuillController = false;
          }
        }
      }
    } finally {
      _isSaving = false;
    }
  }

  void _onTranscriptItemsChanged(List<TranscriptEditItem> items) {
    _transcriptEditItems = items;

    final fullText = TranscriptEditItem.formatToText(items);
    final document = Document();
    document.insert(0, fullText);

    final Delta oldDelta = widget.quillTranscriptController.document.toDelta();
    final Delta newDelta = document.toDelta();

    if (oldDelta != newDelta) {
      _isUpdatingQuillController = true;
      try {
        widget.quillTranscriptController.document = document;
      } finally {
        _isUpdatingQuillController = false;
      }
    }

    widget.cubit.setHasEdits(true);
  }

  @override
  void dispose() {
    _positionSubscription?.cancel();
    _currentPosition.dispose();
    _highlightedIndexNotifier.dispose();
    _focusNode.dispose();
    _scrollDebounce?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(TranscriptPage oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If transcript data changed, rebuild item keys
    if (widget.transcriptJson != oldWidget.transcriptJson) {
      _rebuildItemKeys();
    }

    // If audio player changed, reconnect listeners
    if (widget.audioPlayer != oldWidget.audioPlayer) {
      _positionSubscription?.cancel();
      _setupAudioPlayerListener();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Padding(
              padding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
                bottom: MediaQuery.of(context).padding.bottom +
                  (widget.audioPlayer != null ? 48.h : 16.h),
              ),
              child: widget.transcriptJson.isEmpty
                  ? (widget.transcript.isNotEmpty || _isEditing
                      ? TranscriptEditWidget(
                          focusNode: _focusNode,
                          quillController: widget.quillTranscriptController,
                          scrollController: _scrollController,
                          fontSize: 16,
                          showQuillTool: _isEditing,
                          readOnly: widget.isCommunityNote,
                        )
                      : _buildEmptySummaryView(context))
                  : (_isEditing
                      ? (_useSpecializedEditor
                          ? TranscriptItemsEditor(
                              items: _transcriptEditItems,
                              onItemsChanged: _onTranscriptItemsChanged,
                              readOnly: widget.isCommunityNote,
                              fontSize: 16,
                              initialFocusIndex: _editingTranscriptIndex,
                            )
                          : TranscriptEditWidget(
                              focusNode: _focusNode,
                              quillController: widget.quillTranscriptController,
                              scrollController: _scrollController,
                              fontSize: 16,
                              showQuillTool: true,
                              readOnly: widget.isCommunityNote,
                            ))
                      : ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: widget.transcriptJson.length,
                          itemBuilder: (context, index) =>
                              ValueListenableBuilder<int>(
                            valueListenable: _highlightedIndexNotifier,
                            builder: (context, highlightedIndex, _) => Column(
                              children: [
                                TranscriptLineWidget(
                                  key: _itemKeys[index],
                                  transcriptModel: widget.transcriptJson[index],
                                  isClickable: true,
                                  isHighlighted: index == highlightedIndex,
                                  shouldShowTooltip: index == 0 &&
                                      g
                                              .get<LocalService>()
                                              .hasShownTooltipTranscript() ==
                                          true,
                                  audioPlayer: widget.audioPlayer,
                                  onStartEditing: () => startEditing(
                                      widget.transcriptJson[index]),
                                  isCommunityNote: widget.isCommunityNote,
                                ),
                                if (index == widget.transcriptJson.length - 1)
                                  SizedBox(
                                    height:
                                        MediaQuery.of(context).padding.bottom +
                                            50.h,
                                  ),
                              ],
                            ),
                          ),
                        )),
            ),
          ),
        ),
        KeyboardVisibilityBuilder(
          builder: (context, isKeyboardVisible) {
            return Container(
              height: !isKeyboardVisible ? 0 : kToolbarHeight,
            );
          },
        ),
      ],
    );
  }

  Widget _buildEmptySummaryView(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        context.isLandscape
            ? const SizedBox(height: 80)
            : context.isTablet
                ? const SizedBox(height: 120)
                : AppConstants.kSpacingItem56,
        SvgPicture.asset(
          Assets.icons.icEmptySummary,
        ),
        AppConstants.kSpacingItem8,
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 32.w),
          child: CommonText(
            style: TextStyle(
                fontSize: context.isTablet ? 16 : 14.sp,
                fontWeight: FontWeight.w400),
            S.current.available_transcript,
            textAlign: TextAlign.center,
            textColor: context.colorScheme.mainGray,
          ),
        ),
      ],
    );
  }
}

class TranscriptLineWidget extends StatefulWidget {
  final TranscriptModel transcriptModel;
  final bool isClickable;
  final Color clickEffectColor;
  final bool shouldShowTooltip;
  final bool isHighlighted;
  final AudioPlayer? audioPlayer;
  final VoidCallback? onStartEditing;
  final bool isCommunityNote;

  const TranscriptLineWidget({
    super.key,
    required this.transcriptModel,
    this.isClickable = false,
    this.clickEffectColor = Colors.grey,
    this.shouldShowTooltip = false,
    this.isHighlighted = false,
    this.audioPlayer,
    this.onStartEditing,
    required this.isCommunityNote,
  });

  @override
  State<TranscriptLineWidget> createState() => _TranscriptLineWidgetState();
}

class _TranscriptLineWidgetState extends State<TranscriptLineWidget> {
  late final ElTooltipController _tooltipController;

  @override
  void initState() {
    super.initState();
    _tooltipController = ElTooltipController();

    if (widget.shouldShowTooltip) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.delayed(const Duration(milliseconds: 300), () {
          HapticFeedback.mediumImpact();
          _tooltipController.show();
          g.get<LocalService>().setTooltipTranscriptAsShown();
        });
      });
    }
  }

  @override
  void dispose() {
    _tooltipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final content = Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.only(bottom: 16.h),
      child: Material(
        color: widget.isHighlighted
            ? context.colorScheme.mainBlue.withOpacity(0.24)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8.r),
        child: InkWell(
          onTap: widget.isClickable
              ? () {
                  if (widget.transcriptModel.start >= 0 &&
                      widget.audioPlayer != null) {
                    AnalyticsService.logAnalyticsEventNoParam(
                      eventName: EventName.transcript_content_click,
                    );
                    widget.audioPlayer!.seek(Duration(
                        milliseconds:
                            (widget.transcriptModel.start * 1000).toInt()));
                  }
                }
              : null,
          onDoubleTap: widget.onStartEditing,
          splashColor: widget.isClickable
              ? widget.clickEffectColor.withOpacity(0.3)
              : Colors.transparent,
          highlightColor: widget.isClickable
              ? widget.clickEffectColor.withOpacity(0.1)
              : Colors.transparent,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 2.0.h),
                child: CommonText(
                  formatTranscriptTime(widget.transcriptModel.start),
                  style: TextStyle(
                    fontSize: context.isTablet ? 16 : 14.sp,
                    color: context.colorScheme.mainPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              AppConstants.kSpacingItemW12,
              Expanded(
                child: DirectionalText(
                  text: widget.transcriptModel.text,
                  detectFromContent: true,
                  style: TextStyle(
                    fontSize: context.isTablet ? 18 : 16.sp,
                    fontWeight: FontWeight.w400,
                    color: context.colorScheme.mainPrimary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );

    if (!widget.shouldShowTooltip) return content;

    return ElTooltip(
      content: CommonText(
        S.current.transcript_line_tool_tip,
        style: TextStyle(
          fontSize: context.isTablet ? 17 : 15.sp,
          color: context.colorScheme.themeWhite,
        ),
      ),
      position: ElTooltipPosition.bottomEnd,
      showChildAboveOverlay: false,
      color: context.colorScheme.mainBlue,
      controller: _tooltipController,
      timeout: const Duration(seconds: 5),
      child: content,
    );
  }
}

String formatTranscriptTime(double seconds) {
  seconds = seconds.floor().toDouble();
  int hours = (seconds / 3600).floor();
  int remainingMinutes = ((seconds - hours * 3600) / 60).floor();
  int remainingSeconds = (seconds % 60).floor();
  return '${hours.toString().padLeft(2, '0')}:${remainingMinutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
}
